import { Collapse, Input } from "antd";
import { ChevronDown } from "lucide-react";
import React from "react";

const { Panel } = Collapse;
const { TextArea } = Input;
const JsonContentCollapse = ({ list }) => {
  const childList = (list) => {
    return list?.map((section, index) => ({
      key: section.id,
      label: section.title,
      children: (
        <div className="tw-space-y-4">
          {section.fields.map((field, fieldIndex) => (
            <div key={fieldIndex} className="tw-space-y-2">
              <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700">
                {field.key}
              </label>
              <TextArea
                rows={4}
                value={field.value}
                onChange={(e) => {}}
                className="tw-w-full tw-rounded-lg tw-border tw-border-gray-300 tw-focus:tw-border-blue-500"
              />
            </div>
          ))}
        </div>
      ),
    }));
  };

  return (
    <>
      <Collapse
        size="small"
        defaultActiveKey={list}
        className="tw-bg-transparent"
        expandIcon={({ isActive }) => (
          <ChevronDown
            className={`tw-w-4 tw-h-4 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
              isActive ? "tw-rotate-180" : ""
            }`}
          />
        )}
        expandIconPosition="end"
        items={list?.map((section, index) => ({
          key: section.id,
          label: section.title,
          children: (
            <div className="tw-space-y-4">
              <TextArea
                rows={4}
                value={section.value}
                onChange={(e) => {}}
                className="tw-w-full tw-rounded-lg tw-border tw-border-gray-300 tw-focus:tw-border-blue-500"
              />
              {/* {section.fields.map((field, fieldIndex) => (
                <div key={fieldIndex} className="tw-space-y-2">
                  <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700">
                    {field.key}
                  </label>
                  <TextArea
                    rows={4}
                    value={field.value}
                    onChange={(e) => {}}
                    className="tw-w-full tw-rounded-lg tw-border tw-border-gray-300 tw-focus:tw-border-blue-500"
                  />
                </div>
              ))} */}
            </div>
          ),
        }))}
      />
    </>
  );
};

export default JsonContentCollapse;
