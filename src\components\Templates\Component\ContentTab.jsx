import { Button, Collapse, Input, Row, Col, message } from "antd";
import {
  ChevronDown,
  ChevronUp,
  Upload as UploadIcon,
  ExpandIcon,
  Minimize2,
} from "lucide-react";
import { useState, useEffect, useMemo } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import TemplatePreview from "./TemplatePreview";
import SearchBar from "../../common/SearchBar";
import JsonContentCollapse from "../../common/JsonContentCollapse";

const { Panel } = Collapse;
const { TextArea } = Input;

const ContentTab = ({
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  saving,
  pages,
}) => {
  const [isPageLibraryOpen, setIsPageLibraryOpen] = useState(false); // Hide left sidebar for content tab
  const [expandedPanels, setExpandedPanels] = useState(["0"]); // Default first panel expanded
  const [isAllExpanded, setIsAllExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const placeList = {
    "Hero Section": {
      $section_title: "Your Adventure Awaits!",
      $section_content:
        "Discover and book bus tickets to your favorite destinations with ease. We offer the best prices and a comfortable journey.",
    },
    Popular_Bus_Routes: [
      {
        $title: "Coimbatore Buses",
        $location: "Manali, Jaipur",
      },
      {
        $title: "Mountain Express",
        $location: "Shimla, Ooty",
      },
      {
        $title: "Coastal Rider",
        $location: "Goa, Pondicherry",
      },
    ],
  };
  //   }
  //   useMemo(() => {
  //     return formData.content?.filter((page) =>
  //       searchTerm
  //         ? page?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  //         : true
  //     );
  //   }, [formData.content, searchTerm]);

  // Initialize content structure if not exists
  useEffect(() => {
    if (!formData.content || formData.content.length === 0) {
      // Auto-detect content sections from placeholders
      const initialContent = generateInitialContent();
      setFormData((prev) => ({
        ...prev,
        content: initialContent,
      }));
    }
  }, [formData.placeholders, setFormData]);

  const generateInitialContent = () => {
    const placeholders = formData.placeholders || [];
    const contentSections = [];

    // Group placeholders by section (assuming format like "section.field" or just "field")
    const groupedPlaceholders = {};

    placeholders.forEach((placeholder) => {
      const parts = placeholder.split(".");
      if (parts.length > 1) {
        const sectionName = parts[0];
        const fieldName = parts.slice(1).join(".");

        if (!groupedPlaceholders[sectionName]) {
          groupedPlaceholders[sectionName] = [];
        }
        groupedPlaceholders[sectionName].push({
          key: fieldName,
          value: "",
          placeholder: placeholder,
        });
      } else {
        // Single field, create a general section
        if (!groupedPlaceholders["General"]) {
          groupedPlaceholders["General"] = [];
        }
        groupedPlaceholders["General"].push({
          key: placeholder,
          value: "",
          placeholder: placeholder,
        });
      }
    });

    // Convert grouped placeholders to content sections
    Object.keys(groupedPlaceholders).forEach((sectionName, index) => {
      contentSections.push({
        id: `section_${index}`,
        title:
          sectionName.charAt(0).toUpperCase() +
          sectionName.slice(1).replace(/_/g, " "),
        fields: groupedPlaceholders[sectionName],
      });
    });

    return contentSections.length > 0
      ? contentSections
      : [
          {
            id: "section_0",
            title: "Hero Section",
            fields: [
              {
                key: "title",
                value: "Lorem ipsum dolor sit amet consectetur",
                placeholder: "title",
              },
              {
                key: "subtitle",
                value:
                  "Lorem ipsum dolor sit amet consectetur adipisicing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua",
                placeholder: "subtitle",
              },
            ],
          },
          {
            id: "section_1",
            title: "Section_content",
            fields: [
              {
                key: "content",
                value:
                  "Lorem ipsum dolor sit amet consectetur adipisicing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
                placeholder: "content",
              },
            ],
          },
          {
            id: "section_2",
            title: "Popular_Bus_Routes",
            fields: [
              { key: "position_1", value: "", placeholder: "position_1" },
              { key: "position_2", value: "", placeholder: "position_2" },
              { key: "position_3", value: "", placeholder: "position_3" },
            ],
          },
          {
            id: "section_3",
            title: "Footer",
            fields: [
              { key: "copyright", value: "", placeholder: "copyright" },
              { key: "links", value: "", placeholder: "links" },
            ],
          },
        ];
  };

  const handleContentChange = (sectionId, fieldKey, value) => {
    setFormData((prev) => ({
      ...prev,
      content: prev.content.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              fields: section.fields.map((field) =>
                field.key === fieldKey ? { ...field, value } : field
              ),
            }
          : section
      ),
    }));
  };

  const handleSectionTitleChange = (sectionId, newTitle) => {
    setFormData((prev) => ({
      ...prev,
      content: prev.content.map((section) =>
        section.id === sectionId ? { ...section, title: newTitle } : section
      ),
    }));
  };

  const addNewSection = () => {
    const newSection = {
      id: `section_${Date.now()}`,
      title: "New Section",
      fields: [{ key: "title", value: "", placeholder: "title" }],
    };

    setFormData((prev) => ({
      ...prev,
      content: [...prev.content, newSection],
    }));
  };

  const addFieldToSection = (sectionId) => {
    const fieldName = prompt("Enter field name:");
    if (!fieldName) return;

    setFormData((prev) => ({
      ...prev,
      content: prev.content.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              fields: [
                ...section.fields,
                { key: fieldName, value: "", placeholder: fieldName },
              ],
            }
          : section
      ),
    }));
  };

  const removeSection = (sectionId) => {
    setFormData((prev) => ({
      ...prev,
      content: prev.content.filter((section) => section.id !== sectionId),
    }));
  };

  const toggleExpandAll = () => {
    if (isAllExpanded) {
      setExpandedPanels([]);
    } else {
      setExpandedPanels(
        formData.content?.map((_, index) => index.toString()) || []
      );
    }
    setIsAllExpanded(!isAllExpanded);
  };

  const handleImportJSON = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const jsonData = JSON.parse(e.target.result);
            setFormData((prev) => ({
              ...prev,
              content: jsonData,
            }));
            message.success("JSON imported successfully");
          } catch (error) {
            message.error("Invalid JSON file");
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const handleExportJSON = () => {
    const dataStr = JSON.stringify(formData.content, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "template-content.json";
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleSaveContent = () => {
    handleSubmit();
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        {/* <Row className="tw-w-full tw-h-full"> */}
        {/* Left Side - Template Preview */}
        {/* <Col span={12} className="tw-h-full"> */}
        <TemplatePreview
          isPageLibraryOpen={true}
          //   setIsPageLibraryOpen={setIsPageLibraryOpen}
          isTemplateStructureOpen={true}
          //   setIsTemplateStructureOpen={() => {}} // Disable toggle
          formData={formData}
          setFormData={setFormData}
          pages={pages}
          handleSave={handleSaveContent}
          onCancel={onCancel}
          saving={saving}
          isDrop={false} // Disable drop functionality
        />
        {/* </Col> */}

        {/* Right Side - Content Editor */}
        {/* <Col span={12} className="tw-h-full tw-border-l tw-border-gray-200"> */}
        <div className="tw-h-full tw-flex tw-flex-col tw-bg-white">
          {/* Header */}
          <div className="tw-p-4  ">
            <div>
              <SearchBar handleSearch={(e) => setSearchTerm(e)} />
            </div>
            <div className="tw-flex tw-space-x-4 tw-w-full tw-justify-between tw-items-center">
              <Button
                type="primary"
                size="large"
                onClick={handleImportJSON}
                disabled={saving}
                className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              >
                Import JSON
              </Button>

              <Button
                size="large"
                onClick={toggleExpandAll}
                className="tw-flex tw-w-full tw-items-center tw-text-black tw-border-blue-200 hover:tw-bg-blue-50"
              >
                Expand All
              </Button>
            </div>
          </div>
          <div className="tw-flex-1 tw-overflow-auto tw-p-4">
            <JsonContentCollapse list={placeList} />
            {/* <Collapse
              size="small"
              defaultActiveKey={placeList}
              className="tw-bg-transparent"
              expandIcon={({ isActive }) => (
                <ChevronDown
                  className={`tw-w-4 tw-h-4 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                    isActive ? "tw-rotate-180" : ""
                  }`}
                />
              )}
              expandIconPosition="end"
              items={placeList.map((section, index) => ({
                key: section.id,
                label: section.title,
                children: (
                  <div className="tw-space-y-4">
                    {section.fields.map((field, fieldIndex) => (
                      <div key={fieldIndex} className="tw-space-y-2">
                        <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700">
                          {field.key}
                        </label>
                        <TextArea
                          rows={4}
                          value={field.value}
                          onChange={(e) => {}}
                          className="tw-w-full tw-rounded-lg tw-border tw-border-gray-300 tw-focus:tw-border-blue-500"
                        />
                      </div>
                    ))}
                  </div>
                ),
              }))}
            /> */}
          </div>

          {/* Content Sections */}
          <div className="tw-flex-1 tw-overflow-auto tw-p-4">
            {placeList && placeList?.length > 0 ? (
              <Collapse
                activeKey={expandedPanels}
                onChange={setExpandedPanels}
                className="tw-bg-white tw-border-0"
                expandIcon={({ isActive }) =>
                  isActive ? (
                    <ChevronUp className="tw-w-4 tw-h-4 tw-text-gray-500" />
                  ) : (
                    <ChevronDown className="tw-w-4 tw-h-4 tw-text-gray-500" />
                  )
                }
                ghost
              >
                {placeList.map((section, index) => (
                  <Panel
                    header={
                      <div className="tw-flex tw-items-center tw-justify-between tw-w-full">
                        <Input
                          value={section.title}
                          onChange={(e) =>
                            handleSectionTitleChange(section.id, e.target.value)
                          }
                          className="tw-border-none tw-p-0 tw-font-medium tw-bg-transparent"
                          onClick={(e) => e.stopPropagation()}
                        />
                        <div className="tw-flex tw-items-center tw-space-x-2">
                          <Button
                            size="small"
                            type="text"
                            onClick={(e) => {
                              e.stopPropagation();
                              addFieldToSection(section.id);
                            }}
                            className="tw-text-blue-600 hover:tw-bg-blue-50"
                          >
                            + Add Item
                          </Button>
                          <Button
                            size="small"
                            type="text"
                            danger
                            onClick={(e) => {
                              e.stopPropagation();
                              removeSection(section.id);
                            }}
                            className="tw-text-red-600 hover:tw-bg-red-50"
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    }
                    key={section.id}
                    className="tw-mb-3 tw-border tw-border-gray-200 tw-rounded-lg tw-bg-white"
                  >
                    <div className="tw-space-y-4">
                      {section.fields.map((field, fieldIndex) => (
                        <div key={fieldIndex} className="tw-space-y-2">
                          <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700">
                            {field.key.charAt(0).toUpperCase() +
                              field.key.slice(1).replace(/_/g, " ")}
                          </label>
                          <TextArea
                            value={field.value}
                            onChange={(e) =>
                              handleContentChange(
                                section.id,
                                field.key,
                                e.target.value
                              )
                            }
                            placeholder={`Enter ${field.key}...`}
                            rows={3}
                            className="tw-w-full"
                          />
                        </div>
                      ))}
                    </div>
                  </Panel>
                ))}
              </Collapse>
            ) : (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500 tw-mb-4">
                  No content sections found
                </p>
                <Button type="primary" onClick={addNewSection}>
                  Add First Section
                </Button>
              </div>
            )}

            {/* Add Section Button */}
            {placeList && placeList.length > 0 && (
              <div className="tw-mt-4 tw-text-center">
                <Button
                  type="dashed"
                  onClick={addNewSection}
                  className="tw-w-full tw-h-12 tw-border-2 tw-border-dashed tw-border-gray-300 hover:tw-border-blue-400 tw-text-gray-600 hover:tw-text-blue-600"
                >
                  + Add Section
                </Button>
              </div>
            )}
          </div>
        </div>
        {/* </Col>
        </Row> */}
      </div>
    </DndProvider>
  );
};

export default ContentTab;
