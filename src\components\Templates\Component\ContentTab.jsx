import { Col, Row } from "antd";
import React, { useState } from "react";
import TemplatePreview from "./TemplatePreview";

const ContentTab = ({
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  saving,
  pages,
}) => {
  const [isPageLibraryOpen, setIsPageLibraryOpen] = useState(true);
  const [isTemplateStructureOpen, setIsTemplateStructureOpen] = useState(true);
  const [selectedPageId, setSelectedPageId] = useState(null);
  const [screenSize, setScreenSize] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
  });
  //   const [saving, setSaving] = useState(false);

  const handleSaveContent = () => {};
  return (
    <div>
      <TemplatePreview
        isPageLibraryOpen={isPageLibraryOpen}
        setIsPageLibraryOpen={setIsPageLibraryOpen}
        isTemplateStructureOpen={isTemplateStructureOpen}
        setIsTemplateStructureOpen={setIsTemplateStructureOpen}
        formData={formData}
        setFormData={setFormData}
        pages={pages}
        handleSave={handleSaveContent}
        onCancel={onCancel}
        saving={saving}
        isDrop={false}
      />
      {/* <Row>
            <Col span={12}>

            </Col>
            <Col span={12}>
            </Col>
        </Row> */}
    </div>
  );
};

export default ContentTab;
